import { z } from "zod";
import zodToJsonSchema from "zod-to-json-schema";
import {
  systemInstruction,
  systemQuestionPrompt,
  reportPlanPrompt,
  serpQueriesPrompt,
  queryResultPrompt,
  citationRulesPrompt,
  searchResultPrompt,
  searchKnowledgeResultPrompt,
  reviewPrompt,
  finalReportCitationImagePrompt,
  finalReportReferencesPrompt,
  finalReportPrompt,
} from "@/constants/prompts";

export function getSERPQuerySchema() {
  return z
    .array(
      z
        .object({
          query: z.string().describe("The SERP query (查询词)."),
          language: z.enum(["chinese", "english"]).describe("Language of the query (查询语言)."),
          groupId: z.string().optional().describe("Optional group ID to associate related queries (可选的组ID用于关联相关查询)."),
          researchGoal: z
            .string()
            .describe(
              "First talk about the goal of the research that this query is meant to accomplish, then go deeper into how to advance the research once the results are found, mention additional research directions. Be as specific as possible, especially for additional research directions. JSON reserved words should be escaped."
            ),
        })
        .required({ query: true, language: true, researchGoal: true })
    )
    .describe(`List of SERP queries with language specification.`);
}

export function getSERPQueryOutputSchema() {
  const SERPQuerySchema = getSERPQuerySchema();
  return JSON.stringify(zodToJsonSchema(SERPQuerySchema), null, 4);
}

export function getSystemPrompt() {
  return systemInstruction.replace("{now}", new Date().toISOString());
}

export function generateQuestionsPrompt(query: string) {
  return systemQuestionPrompt.replace("{query}", query);
}

export function writeReportPlanPrompt(query: string) {
  return reportPlanPrompt.replace("{query}", query);
}

export function generateSerpQueriesPrompt(plan: string, searchMode: SearchMode = "bilingual") {
  return serpQueriesPrompt
    .replace("{plan}", plan)
    .replace("{searchMode}", searchMode)
    .replace("{outputSchema}", getSERPQueryOutputSchema());
}

export function processResultPrompt(query: string, researchGoal: string) {
  return queryResultPrompt
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal);
}

export function processSearchResultPrompt(
  query: string,
  researchGoal: string,
  results: Source[],
  enableReferences: boolean
) {
  const context = results.map(
    (result, idx) =>
      `<content index="${idx + 1}" url="${result.url}">\n${
        result.content
      }\n</content>`
  );
  return (
    searchResultPrompt + (enableReferences ? `\n\n${citationRulesPrompt}` : "")
  )
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal)
    .replace("{context}", context.join("\n"));
}

export function processSearchKnowledgeResultPrompt(
  query: string,
  researchGoal: string,
  results: Knowledge[]
) {
  const context = results.map(
    (result, idx) =>
      `<content index="${idx + 1}" url="${location.host}">\n${
        result.content
      }\n</content>`
  );
  return searchKnowledgeResultPrompt
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal)
    .replace("{context}", context.join("\n"));
}

export function reviewSerpQueriesPrompt(
  plan: string,
  learning: string[],
  suggestion: string,
  searchMode: SearchMode = "bilingual"
) {
  const learnings = learning.map(
    (detail) => `<learning>\n${detail}\n</learning>`
  );
  return reviewPrompt
    .replace("{plan}", plan)
    .replace("{learnings}", learnings.join("\n"))
    .replace("{suggestion}", suggestion)
    .replace("{searchMode}", searchMode)
    .replace("{outputSchema}", getSERPQueryOutputSchema());
}

export function writeFinalReportPrompt(
  plan: string,
  learning: string[],
  source: Source[],
  images: ImageSource[],
  requirement: string,
  enableCitationImage: boolean,
  enableReferences: boolean
) {
  const learnings = learning.map(
    (detail) => `<learning>\n${detail}\n</learning>`
  );
  const sources = source.map(
    (item, idx) =>
      `<source index="${idx + 1}" url="${item.url}">\n${item.title}\n</source>`
  );
  const imageList = images.map(
    (source, idx) => `${idx + 1}. ![${source.description}](${source.url})\n   (图片说明：${source.chineseDescription || source.description})`
  );
  return (
    finalReportPrompt +
    (enableCitationImage
      ? `\n**Including meaningful images from the previous research in the report is very helpful.**\n\n${finalReportCitationImagePrompt}`
      : "") +
    (enableReferences ? `\n\n${finalReportReferencesPrompt}` : "")
  )
    .replace("{plan}", plan)
    .replace("{learnings}", learnings.join("\n"))
    .replace("{sources}", sources.join("\n"))
    .replace("{images}", imageList.join("\n"))
    .replace("{requirement}", requirement);
}

// Adversarial plan optimization related Schema and Prompt functions
export const planTurnSchema = {
  type: 'object',
  properties: {
    thought: { 
      type: 'string', 
      description: "Your thought process. If you are the architect, explain why you designed the plan this way. If you are the critic, explain why you raised these critical opinions." 
    },
    action: { 
      type: 'string', 
      enum: ['propose_plan', 'critique_plan', 'finish_discussion'],
      description: "Your action: propose_plan (architect proposes or modifies plan), critique_plan (critic provides feedback), finish_discussion (critic ends discussion and finalizes the plan)."
    },
    plan: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          section_title: { type: 'string' },
          summary: { type: 'string', description: "Summarize the core content of this section in one sentence." }
        },
        required: ['section_title', 'summary']
      },
      nullable: true,
      description: "Report plan. Only provide when action is 'propose_plan' or 'finish_discussion'."
    },
    critique: {
      oneOf: [
        { type: 'string' },
        {
          type: 'object',
          properties: {
            topic_relevance: {
              type: 'array',
              items: { type: 'string' }
            },
            completeness: {
              type: 'array', 
              items: { type: 'string' }
            },
            non_redundancy: {
              type: 'array',
              items: { type: 'string' }
            },
            conciseness: {
              type: 'array',
              items: { type: 'string' }
            },
            abstraction_guidance: {
              type: 'array',
              items: { type: 'string' }
            },
            logical_flow: {
              type: 'array',
              items: { type: 'string' }
            }
          }
        }
      ],
      nullable: true,
      description: "REQUIRED when action is 'critique_plan': Specific, actionable criticism and improvement suggestions. Can be either a string of feedback or a structured object with categorized critiques. MUST be null/empty for other actions."
    },
    finish_reason: {
      type: 'string',
      nullable: true,
      description: "Reason for finalizing the plan. Only provide when action is 'finish_discussion'."
    }
  },
  required: ['thought', 'action'],
  additionalProperties: false,
};

// Interface for adversarial mechanism prompt parameters
interface ReportPlanPromptParams {
  nextPersona: '架构师' | '批判者';
  query: string;
  conversationText: string;
  isFirstTurn: boolean;
  mode?: 'fixed' | 'auto';
  maxRounds?: number;
  currentRound?: number;
}

// Function to generate Architect prompt (specialized for plan creation)
export const getArchitectPrompt = ({
  query,
  conversationText,
  isFirstTurn,
  mode = 'auto',
  maxRounds = 5,
  currentRound = 1
}: Omit<ReportPlanPromptParams, 'nextPersona'>): string => `
You are an AI research assistant acting as the **Architect**. Your task is to create and refine high-quality report outlines.
Your response must be a single JSON object that follows the predefined schema, and use Chinese as outline language.

**Core Research Topic (from user):**
<QUERY>
${query}
</QUERY>

**Current Plan Discussion Process:**
${conversationText || "The conversation has not started yet."}

--- Architect Role & Responsibilities ---

**Your Role:** You are the **Architect** - responsible for creating and revising the report outline (plan). You analyze the user's query and any feedback to propose a structurally sound and comprehensive plan.

**Output Format:** Your JSON response must contain:
- \`thought\`: Your internal reasoning process - explain WHY you're designing the plan this way
- \`action\`: Always "propose_plan" for your role
- \`plan\`: Array of section objects, each MUST have EXACTLY these two fields:
  - \`section_title\`: The chapter/section title (NOT "章节", NOT "标题", MUST be "section_title")
  - \`summary\`: The chapter content summary (NOT "目标", NOT "摘要", MUST be "summary")
  
**CRITICAL: Use exact field names "section_title" and "summary" - DO NOT use Chinese field names like "章节" or "目标"**

**Example of CORRECT plan format:**
\`\`\`json
{
  "thought": "...",
  "action": "propose_plan",
  "plan": [
    {
      "section_title": "第一章 引言与研究目的",
      "summary": "本章节为报告第1章。本章节的研究目标是：..."
    }
  ]
}
\`\`\`

**Architectural Principles:**
1. **Stay on Topic:** Every section must strictly revolve around the user's core query
2. **Clear Structure:** Each \`summary\` must follow format "本章节为报告第X章。本章节的研究目标是：..." - describe what to investigate, not what's already found
3. **Be Concise:** Merge related content, remove redundant sections
4. **Abstract Framework:** Serve as research scaffold - avoid specific examples, use "examine key cases" not "examine Company A and B"
5. **Address Feedback:** Systematically address all criticism, explain reasoning if you deviate from suggestions
6. **Chapter Independence:** Each chapter should be self-contained and independently researchable

${isFirstTurn ? `**First Round:** As this is the first round, your action must be 'propose_plan'.` : ''}

${mode === 'fixed' ? `**Fixed Mode:** This is round ${currentRound}/${maxRounds}. Continue until final round.` : ''}

**Output Requirements:**
- Valid JSON matching the schema
- Chinese language for outline content
- Focus on plan creation and refinement
`;
{
  "thought": "...",
  "action": "propose_plan",
  "plan": [
    {
      "section_title": "第一章 引言与研究目的",
      "summary": "本章节为报告第1章。本章节的研究目标是：..."
    }
  ]
}
```

**Architectural Principles:**
1. **Stay on Topic:** Every section must strictly revolve around the user's core query
2. **Clear Structure:** Each \`summary\` must follow format "本章节为报告第X章。本章节的研究目标是：..." - describe what to investigate, not what's already found
3. **Be Concise:** Merge related content, remove redundant sections
4. **Abstract Framework:** Serve as research scaffold - avoid specific examples, use "examine key cases" not "examine Company A and B"
5. **Address Feedback:** Systematically address all criticism, explain reasoning if you deviate from suggestions
6. **Chapter Independence:** Each chapter should be self-contained and independently researchable

${isFirstTurn ? `**First Round:** As this is the first round, your action must be 'propose_plan'.` : ''}

${mode === 'fixed' ? `**Fixed Mode:** This is round ${currentRound}/${maxRounds}. Continue until final round.` : ''}

**Output Requirements:**
- Valid JSON matching the schema
- Chinese language for outline content
- Focus on plan creation and refinement
`;

// Function to generate Critic prompt (specialized for plan evaluation) 
export const getCriticPrompt = ({
  query,
  conversationText,
  mode = 'auto',
  maxRounds = 5,
  currentRound = 1
}: Omit<ReportPlanPromptParams, 'nextPersona' | 'isFirstTurn'>): string => `
You are an AI research assistant acting as the **Critic**. Your task is to rigorously evaluate report outlines and decide when they meet quality standards.
Your response must be a single JSON object that follows the predefined schema, and use Chinese as outline language.

**Core Research Topic (from user):**
<QUERY>
${query}
</QUERY>

**Current Plan Discussion Process:**
${conversationText || "The conversation has not started yet."}

--- Critic Role & Responsibilities ---

**Your Role:** You are the **Critic** - the quality gatekeeper who reviews plans and decides when discussion ends. Only you can finalize the plan.

**Output Format for Critique:** When providing feedback:
- \`thought\`: Your evaluation reasoning - explain WHY you're giving this feedback
- \`action\`: "critique_plan"
- \`critique\`: Specific, actionable improvement suggestions (string or structured object)

**Output Format for Finalization:** When plan is satisfactory:
- \`thought\`: Explain why the plan is now acceptable
- \`action\`: "finish_discussion"  
- \`plan\`: The final approved plan structure - Array of objects with EXACTLY these fields:
  - \`section_title\`: The chapter/section title (NOT "章节", NOT "标题", MUST be "section_title")
  - \`summary\`: The chapter content summary (NOT "目标", NOT "摘要", MUST be "summary")
- \`finish_reason\`: Why you're ending the discussion

**CRITICAL: Use exact field names "section_title" and "summary" - DO NOT use Chinese field names**

**Example of CORRECT finish_discussion format:**
```json
{
  "thought": "...",
  "action": "finish_discussion",
  "plan": [
    {
      "section_title": "第一章 引言与研究目的",
      "summary": "本章节为报告第1章。本章节的研究目标是：..."
    }
  ],
  "finish_reason": "..."
}
```

**Evaluation Criteria:**
1. **Topic Relevance:** Do sections closely align with the user's query?
2. **Completeness:** Are all key aspects covered? Any gaps?
3. **Non-redundancy:** Any content overlap between sections?
4. **Appropriate Granularity:** Right level of detail? Avoid over-merging distinct topics
5. **Abstraction & Guidance:** Flexible framework vs overly prescriptive examples?
6. **Logical Flow:** Natural transitions and progressive structure?

${mode === 'fixed' ? (
  currentRound === maxRounds 
    ? `**Fixed Mode - FINAL ROUND:** This is the FINAL round (${currentRound}/${maxRounds}). You MUST use 'finish_discussion' action to conclude the optimization process. You cannot use 'critique_plan' in this round.`
    : `**Fixed Mode:** This is round ${currentRound}/${maxRounds}. Continue critiquing until the final round ${maxRounds}, where you must use 'finish_discussion'.`
) : ''}

**Critical Requirements:**
- For 'critique_plan': MUST provide specific feedback in \`critique\` field
- For 'finish_discussion': MUST provide final \`plan\` and \`finish_reason\`
- Valid JSON matching the schema
`;

// Function to generate adversarial mechanism prompts (legacy compatibility wrapper)
export const getReportPlanPrompt = ({
  nextPersona,
  query,
  conversationText,
  isFirstTurn,
  mode = 'auto',
  maxRounds = 5,
  currentRound = 1
}: ReportPlanPromptParams): string => {
  if (nextPersona === '架构师') {
    return getArchitectPrompt({
      query,
      conversationText,
      isFirstTurn,
      mode,
      maxRounds,
      currentRound
    });
  } else {
    return getCriticPrompt({
      query,
      conversationText,
      mode,
      maxRounds,
      currentRound
    });
  }
};
