// 章节式研究工具函数

import { ChapterInfo } from '@/types/chapter-research';

// 解析计划文本为章节列表
export function parsePlanToChapters(planText: string): ChapterInfo[] {
  console.log('🔍 [计划解析] 开始解析计划文本:', planText.substring(0, 200));
  
  try {
    // 首先尝试解析JSON格式（新的统一格式）
    if (planText.trim().startsWith('{')) {
      const planJson = JSON.parse(planText);
      if (planJson.sections && Array.isArray(planJson.sections)) {
        const chapters = planJson.sections.map((section: any, index: number): ChapterInfo => {
          const chapterId = `chapter-${index + 1}-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
          return {
            id: chapterId,
            title: section.section_title || `第${index + 1}章`,
            goal: section.summary || '研究目标待定',
            status: 'pending',
            currentRound: 0,
            currentPhase: 'high_level',
          };
        });
        
        console.log('✅ [计划解析] JSON格式解析成功，章节数:', chapters.length);
        return chapters;
      }
    }
    
    // 如果不是JSON格式，尝试解析文本格式（兼容旧格式）
    const chapters = parseTextPlanToChapters(planText);
    console.log('✅ [计划解析] 文本格式解析成功，章节数:', chapters.length);
    return chapters;
    
  } catch (error) {
    console.error('❌ [计划解析] JSON解析失败，尝试文本解析:', error);
    
    // JSON解析失败，回退到文本解析
    const chapters = parseTextPlanToChapters(planText);
    console.log('✅ [计划解析] 文本格式回退解析成功，章节数:', chapters.length);
    return chapters;
  }
}

// 解析文本格式的计划
function parseTextPlanToChapters(planText: string): ChapterInfo[] {
  const lines = planText.split('\n').filter(line => line.trim());
  const chapters: ChapterInfo[] = [];
  
  let currentChapter: Partial<ChapterInfo> | null = null;
  let chapterIndex = 0;
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // 检测章节标题（各种可能的格式）
    const chapterTitlePatterns = [
      /^#+\s*(.+)$/,           // ## 标题
      /^第?(\d+)[章节部分][:：]?\s*(.+)$/,  // 第1章：标题
      /^(\d+)[\.\)]\s*(.+)$/,  // 1. 标题 或 1) 标题
      /^[一二三四五六七八九十]+[、\.]\s*(.+)$/,  // 一、标题
    ];
    
    let isChapterTitle = false;
    let title = '';
    
    for (const pattern of chapterTitlePatterns) {
      const match = trimmedLine.match(pattern);
      if (match) {
        isChapterTitle = true;
        // 提取标题，处理不同的匹配组
        if (match.length > 2) {
          title = match[2]; // 有序号的情况
        } else {
          title = match[1]; // 简单标题的情况
        }
        break;
      }
    }
    
    if (isChapterTitle) {
      // 如果之前有章节，先保存它
      if (currentChapter && currentChapter.title) {
        chapters.push(finalizeChapter(currentChapter, chapterIndex));
        chapterIndex++;
      }
      
      // 开始新章节
      const chapterId = `chapter-${chapterIndex + 1}-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
      currentChapter = {
        id: chapterId,
        title: title.trim(),
        goal: '',
        status: 'pending',
        currentRound: 0,
        currentPhase: 'high_level',
      };
    } else if (currentChapter && trimmedLine) {
      // 累积章节描述
      if (currentChapter.goal) {
        currentChapter.goal += ' ' + trimmedLine;
      } else {
        currentChapter.goal = trimmedLine;
      }
    }
  }
  
  // 保存最后一个章节
  if (currentChapter && currentChapter.title) {
    chapters.push(finalizeChapter(currentChapter, chapterIndex));
  }
  
  return chapters;
}

// 完善章节信息
function finalizeChapter(chapter: Partial<ChapterInfo>, index: number): ChapterInfo {
  return {
    id: chapter.id || `chapter-${index + 1}-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
    title: chapter.title || `第${index + 1}章`,
    goal: chapter.goal || '研究目标待定',
    status: 'pending',
    currentRound: 0,
    currentPhase: 'high_level',
  };
}

// 验证章节解析结果
export function validateChapters(chapters: ChapterInfo[]): boolean {
  if (chapters.length === 0) {
    console.warn('⚠️ [章节验证] 没有解析到任何章节');
    return false;
  }
  
  if (chapters.length > 10) {
    console.warn('⚠️ [章节验证] 章节数量过多，可能影响性能:', chapters.length);
  }
  
  const invalidChapters = chapters.filter(chapter => 
    !chapter.id || !chapter.title || !chapter.goal
  );
  
  if (invalidChapters.length > 0) {
    console.warn('⚠️ [章节验证] 发现无效章节:', invalidChapters);
    return false;
  }
  
  console.log('✅ [章节验证] 章节验证通过，有效章节数:', chapters.length);
  return true;
}

// 获取章节的收集信息摘要 - 优化格式和性能
export function getChapterCollectedSummary(chapterId: string, collectedInfo: any[]): string {
  const chapterInfo = collectedInfo.filter(info => info.chapterId === chapterId);
  if (chapterInfo.length === 0) {
    return '暂无收集信息';
  }
  
  // 按轮次组织信息，提供更清晰的结构
  const roundInfoMap = new Map<number, any>();
  chapterInfo.forEach(info => {
    if (!roundInfoMap.has(info.roundNumber)) {
      roundInfoMap.set(info.roundNumber, info);
    }
  });
  
  const formattedInfo = Array.from(roundInfoMap.values())
    .sort((a, b) => a.roundNumber - b.roundNumber)
    .map(info => {
      const searchCount = info.searchResults?.length || 0;
      return `第${info.roundNumber}轮: ${info.summary} (查询${searchCount}个)`;
    })
    .join('\n');
  
  return formattedInfo || '暂无摘要信息';
}

// 生成章节研究状态报告
export function generateChapterStatusReport(chapters: ChapterInfo[]): string {
  const statusCounts = {
    pending: 0,
    discussing: 0,
    searching: 0,
    writing: 0,
    completed: 0,
  };
  
  chapters.forEach(chapter => {
    statusCounts[chapter.status]++;
  });
  
  return `
章节研究进度报告：
- 总章节数：${chapters.length}
- 待开始：${statusCounts.pending}
- 讨论中：${statusCounts.discussing}
- 搜索中：${statusCounts.searching}
- 写作中：${statusCounts.writing}
- 已完成：${statusCounts.completed}
  `.trim();
}