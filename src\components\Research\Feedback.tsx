"use client";
import dynamic from "next/dynamic";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader<PERSON>ircle, <PERSON>rk<PERSON> } from "lucide-react";
import { Button } from "@/components/Internal/Button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import useDeepResearch from "@/hooks/useDeepResearch";
import useAccurateTimer from "@/hooks/useAccurateTimer";
import { useTaskStore } from "@/store/task";
import { useChapterResearch } from "@/hooks/useChapterResearch";
import AdversarialPlanOptimizer from "./AdversarialPlanOptimizer";
import { ChapterResearchConfig } from "./ChapterResearchConfig";
import { ChapterDiscussionViewer } from "./ChapterDiscussionViewer";
import { ChapterInfoCollector } from "./ChapterInfoCollector";
import { ChapterReportController } from "./ChapterReportController";

const MagicDown = dynamic(() => import("@/components/MagicDown"));

const formSchema = z.object({
  feedback: z.string(),
});

function Feedback() {
  const { t } = useTranslation();
  const taskStore = useTaskStore();
  const { status, deepResearch, writeReportPlan } = useDeepResearch();
  const {
    formattedTime,
    start: accurateTimerStart,
    stop: accurateTimerStop,
  } = useAccurateTimer();
  const [isThinking, setIsThinking] = useState<boolean>(false);
  const [isResearch, setIsResaerch] = useState<boolean>(false);
  const [showAdversarialOptimizer, setShowAdversarialOptimizer] = useState<boolean>(false);
  
  // 章节式研究相关
  const { 
    chapterResearch, 
    showConfig: showChapterConfig, 
    isActive: isChapterActive 
  } = useChapterResearch();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      feedback: taskStore.feedback,
    },
  });

  async function startDeepResearch() {
    try {
      accurateTimerStart();
      setIsResaerch(true);
      await deepResearch();
    } finally {
      setIsResaerch(false);
      accurateTimerStop();
    }
  }

  async function handleSubmit(values: z.infer<typeof formSchema>) {
    taskStore.resetAdversarialPlan(); // 清除旧状态
    const { question, questions, setFeedback, setQuery } = useTaskStore.getState();
    setFeedback(values.feedback);
    const prompt = [
      `Initial Query: ${question}`,
      `Follow-up Questions: ${questions}`,
      `Follow-up Feedback: ${values.feedback}`,
    ].join("\n\n");
    setQuery(prompt); // 只更新query，不触发plan生成
  }

  async function handleWriteStandardPlan() {
    if (!taskStore.query) return;
    try {
      accurateTimerStart();
      setIsThinking(true);
      await writeReportPlan();
    } finally {
      setIsThinking(false);
      accurateTimerStop();
    }
  }

  function handleOptimizePlan() {
    if (!taskStore.query) return;
    setShowAdversarialOptimizer(true);
  }

  function handleChapterResearch() {
    if (!taskStore.reportPlan) {
      alert('请先生成研究计划');
      return;
    }
    // 只显示配置面板，不自动开始研究
    showChapterConfig();
  }

  useEffect(() => {
    form.setValue("feedback", taskStore.feedback);
  }, [taskStore.feedback, form]);

  return (
    <section className="p-4 border rounded-md mt-4 print:hidden">
      <h3 className="font-semibold text-lg border-b mb-2 leading-10">
        {t("research.feedback.title")}
      </h3>
      {taskStore.questions === "" ? (
        <div>{t("research.feedback.emptyTip")}</div>
      ) : (
        <div>
          <h4 className="mt-4 text-base font-semibold">
            {t("research.feedback.questions")}
          </h4>
          <MagicDown
            className="mt-2 min-h-20"
            value={taskStore.questions}
            onChange={(value) => taskStore.updateQuestions(value)}
          ></MagicDown>
          <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)}>
                <FormField
                control={form.control}
                name="feedback"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="mb-2 font-semibold">
                      {t("research.feedback.feedbackLabel")}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        rows={3}
                        placeholder={t("research.feedback.feedbackPlaceholder")}
                        disabled={isThinking}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              {!taskStore.query ? (
                <Button
                  className="mt-4 w-full"
                  type="submit"
                  disabled={isThinking}
                >
                  {t("research.feedback.confirmFeedback", "Confirm Feedback & Proceed")}
                </Button>
              ) : (
                <div className="mt-4 space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Button
                      className="w-full"
                      onClick={handleWriteStandardPlan}
                      disabled={isThinking || taskStore.adversarialPlan?.isRunning || chapterResearch.isRunning}
                    >
                      {isThinking && !taskStore.adversarialPlan?.isRunning ? (
                        <>
                          <LoaderCircle className="animate-spin" />
                          <span>{status}</span>
                          <small className="font-mono">{formattedTime}</small>
                        </>
                      ) : (
                        t("research.common.writeReportPlan")
                      )}
                    </Button>
                    
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={handleOptimizePlan}
                      disabled={isThinking || taskStore.adversarialPlan?.isRunning || chapterResearch.isRunning}
                    >
                      <Sparkles className="mr-2 h-4 w-4" />
                      {t("research.adversarial.optimizePlan", "Advanced Plan Optimization")}
                    </Button>
                  </div>
                </div>
              )}
            </form>
          </Form>
        </div>
      )}

      {showAdversarialOptimizer && (
        <div className="mt-6">
          <AdversarialPlanOptimizer onClose={() => setShowAdversarialOptimizer(false)} />
        </div>
      )}

      {taskStore.reportPlan !== "" ? (
        <div className="mt-6">
          <h4 className="text-base font-semibold">
            {t("research.feedback.reportPlan")}
          </h4>
          <MagicDown
            className="mt-2 min-h-20"
            value={taskStore.reportPlan}
            onChange={(value) => taskStore.updateReportPlan(value)}
          ></MagicDown>
          
          {/* 研究选项 */}
          <div className="space-y-3 mt-4">
            <Button
              className="w-full"
              variant="default"
              onClick={() => startDeepResearch()}
              disabled={isResearch || chapterResearch.isRunning}
            >
              {isResearch ? (
                <>
                  <LoaderCircle className="animate-spin" />
                  <span>{status}</span>
                  <small className="font-mono">{formattedTime}</small>
                </>
              ) : taskStore.tasks.length === 0 ? (
                t("research.common.startResearch")
              ) : (
                t("research.common.restartResearch")
              )}
            </Button>
            
            <Button
              variant="secondary"
              className="w-full"
              onClick={handleChapterResearch}
              disabled={isResearch || chapterResearch.isRunning}
            >
              📚 {t("research.chapter.startChapterResearch", "章节式研究")}
            </Button>
          </div>
        </div>
      ) : null}

      {/* 章节式研究界面 */}
      <div className="mt-6 space-y-6">
        <ChapterResearchConfig />
        {isChapterActive && (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <ChapterDiscussionViewer showAllChapters={true} />
              <ChapterReportController />
            </div>
            <ChapterInfoCollector />
          </>
        )}
      </div>
    </section>
  );
}

export default Feedback;
